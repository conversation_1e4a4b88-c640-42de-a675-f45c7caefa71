<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>翻譯測試</title>
</head>
<body>
    <h1>翻譯函數測試</h1>
    <div id="test-results"></div>
    
    <script src="utils/translations.js"></script>
    <script>
        console.log('🧪 開始翻譯函數測試...');
        
        const results = document.getElementById('test-results');
        let html = '<h2>測試結果:</h2>';
        
        // 測試 window.getTranslation 是否存在
        html += `<p>window.getTranslation 存在: ${typeof window.getTranslation === 'function'}</p>`;
        
        if (typeof window.getTranslation === 'function') {
            // 測試翻譯功能
            const testKeys = ['title', 'spinButton', 'breakfast', 'lunch', 'dinner'];
            testKeys.forEach(key => {
                const translation = window.getTranslation('zh', key);
                html += `<p>${key}: "${translation}"</p>`;
            });
        } else {
            html += '<p style="color: red;">翻譯函數未載入！</p>';
        }
        
        results.innerHTML = html;
        console.log('🧪 翻譯函數測試完成');
    </script>
</body>
</html>