When project updates occur
- Always check if README.md in trickle/notes needs updates
- Update feature list when new functionality is added
- Update technical stack when dependencies change
- Update component list when new components are created
- Update data structure when models change
- Keep development notes current with latest implementation
- Update next steps based on project progress and user feedback