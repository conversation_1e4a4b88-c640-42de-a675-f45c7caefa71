name: Deploy to Firebase Hosting

on:
  push:
    branches:
      - main # 或您的主要分支名稱

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Install Dependencies
        run: npm ci # 直接在根目錄下執行

      - name: Build
        run: npm run build # 直接在根目錄下執行

      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_TRIBE_RESTAURANT_ROULETTE }}'
          channelId: live
          projectId: tribe-restaurant-roulette
          # 移除 working-directory: hosting
          # Firebase Hosting 會根據 firebase.json 中的 public 設定來部署
