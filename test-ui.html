<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI測試</title>
    <style>
        .test-result { padding: 10px; margin: 10px 0; border: 1px solid #ccc; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
    </style>
</head>
<body>
    <h1>UI翻譯測試</h1>
    <div id="test-results"></div>
    
    <script src="utils/translations.js"></script>
    <script>
        setTimeout(() => {
            const results = document.getElementById('test-results');
            let html = '';
            
            // 測試 window.getTranslation 是否存在
            if (typeof window.getTranslation === 'function') {
                html += '<div class="test-result success">✅ window.getTranslation 函數已載入</div>';
                
                // 測試翻譯功能
                const testKeys = [
                    { key: 'title', expected: '吃這家' },
                    { key: 'spinButton', expected: '想吃什麼？' },
                    { key: 'breakfast', expected: '早餐' },
                    { key: 'lunch', expected: '午餐' },
                    { key: 'dinner', expected: '晚餐' }
                ];
                
                testKeys.forEach(test => {
                    const translation = window.getTranslation('zh', test.key);
                    if (translation === test.expected) {
                        html += `<div class="test-result success">✅ ${test.key}: "${translation}"</div>`;
                    } else {
                        html += `<div class="test-result error">❌ ${test.key}: "${translation}" (應為: "${test.expected}")</div>`;
                    }
                });
                
            } else {
                html += '<div class="test-result error">❌ window.getTranslation 函數未載入</div>';
                html += `<div class="test-result error">詳細資訊: ${typeof window.getTranslation}</div>`;
                html += `<div class="test-result error">window.TRANSLATIONS: ${typeof window.TRANSLATIONS}</div>`;
            }
            
            results.innerHTML = html;
        }, 500);
    </script>
</body>
</html>