# 🍽️ Restaurant Roulette - 吃這家

智能餐廳推薦應用程式，使用 Google Places API 搜索附近真實餐廳，支援多國語言與智能路線規劃。

## ✨ 功能特色

### 🎰 輪盤式餐廳選擇
- 有趣的旋轉動畫介面
- 隨機推薦機制，驚喜每一餐

### 🌍 智能定位服務
- 自動偵測用戶位置
- 手動輸入地址支援
- 可調整搜索半徑（1-20公里）
- 儲存常用地點（家/辦公室）

### 🍜 用餐時段篩選
- 早餐/午餐/晚餐時段選擇
- 智能營業時間判斷
- 即時營業狀態顯示

### 🗺️ 路線規劃導航
- 自動計算到餐廳的最佳路線
- 一鍵開啟 Google Maps 導航
- 顯示距離和預估時間

### 🌐 多國語言支援
- 支援 6 種語言：繁體中文、英文、日文、韓文、西班牙文、法文
- 根據語言偏好顯示餐廳資訊
- 地址本地化顯示

### 📱 響應式設計
- 支援桌面與行動裝置
- 現代化 UI/UX 設計
- 深色主題介面

## 🛠️ 技術架構

### 核心技術
- Pure JavaScript + React (CDN)
- Google Places API 整合
- Google Maps JavaScript API
- 響應式 CSS 設計

### API 整合
- **Google Places API**：餐廳搜索與詳細資訊
- **Geocoding API**：地址轉換與反向地理編碼
- **Maps API**：路線規劃與導航

### 部署方式
- Firebase Hosting 自動部署
- GitHub Actions CI/CD 整合
- 環境變數安全管理

## 📋 使用說明

1. **開啟應用程式**：訪問部署的網站
2. **選擇語言**：點選偏好的語言介面
3. **允許定位**：同意瀏覽器獲取位置權限
4. **設定搜索條件**：
   - 選擇用餐時段
   - 調整搜索半徑
5. **轉動輪盤**：點擊「轉動尋找餐廳」
6. **查看結果**：
   - 瀏覽餐廳詳細資訊
   - 點擊導航前往餐廳

## 🎯 智能功能

### 自動回退機制
- Google Places API 優先
- 失敗時切換模擬數據
- 保持應用程式持續可用

### 地址簡化顯示
- 智能解析 Google 地址
- 台灣地址格式優化
- 去除冗餘資訊

### 錯誤處理
- 詳細錯誤記錄
- 用戶友善提示
- 重試機制

## 🔐 隱私安全

- 位置資料僅用於餐廳搜索
- 不儲存個人資訊
- API 金鑰域名限制
- 安全的環境變數管理

## 🚀 開發部署

### 本地開發
```bash
# 直接開啟 index.html 即可運行
# 或使用 Live Server
```

### 部署流程
- GitHub 推送自動觸發
- GitHub Actions 執行建置
- Firebase Hosting 自動更新

## 🤝 協作規則

- 修改完成後自動 Git Commit
- 使用繁體中文回應
- 最小化程度修改，避免過度優化

---

**版本**: v3.0 - 多語言智能導航版  
**最後更新**: 2025-08-01